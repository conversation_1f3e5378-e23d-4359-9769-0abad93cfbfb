# Generated manually to fix Integration-Technology relationship

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0009_refactor_technology_relationship'),
    ]

    operations = [
        # Remove the old technology_id field that wasn't properly removed in migration 0009
        migrations.RunSQL(
            "ALTER TABLE integrations_integration DROP COLUMN IF EXISTS technology_id;",
            reverse_sql="ALTER TABLE integrations_integration ADD COLUMN technology_id VARCHAR(255);"
        ),
    ]
