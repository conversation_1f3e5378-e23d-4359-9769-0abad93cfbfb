# Generated by Django 4.2.20 on 2025-06-18 06:19

from django.db import migrations, models
import django.db.models.deletion


def populate_technology_data(apps, schema_editor):
    """Populate Technology table with data and update Integration references."""
    Technology = apps.get_model("integrations", "Technology")
    Integration = apps.get_model("integrations", "Integration")

    # Technology ID to name mapping
    technology_names = {
        "absolute": "Absolute",
        "azure_ad": "Microsoft Entra ID (Azure AD)",
        "carbon_black": "Carbon Black",
        "cb_cloud": "Carbon Black Cloud Endpoint Standard",
        "cb_threat_hunter": "Carbon Black Threat Hunter",
        "cisco_duo": "Duo",
        "cisco_ise": "Cisco Ise AssetView",
        "commvault": "Commvault",
        "cortex_xdr": "Palo Alto Cortex XDR",
        "crowdstrike_falcon": "CrowdStrike Falcon",
        "cyberark_epm": "CyberArk Endpoint Privilege Manager",
        "cylance": "Cylance",
        "defender_atp": "Microsoft Defender for Endpoint",
        "extrahop_revealx_360": "ExtraHop RevealX 360",
        "falcon_em": "CrowdStrike Discover",
        "freshworks_service": "Freshworks Freshservice",
        "import_hosts": "Import Hosts",
        "infoblox_ddi": "Infoblox DDI",
        "ivanti_neurons": "Ivanti Neurons",
        "ivanti_pm": "Ivanti Patch Management",
        "jamf_pro": "JAMF Pro",
        "ms_intune": "Microsoft Intune",
        "netapp_ontap": "NetApp ONTAP",
        "qualys_gav": "Qualys Global AssetView",
        "qualys_vmpc": "Qualys VMDR",
        "rapid7_insightvm": "Rapid7 InsightVM",
        "s1_ranger": "Sentinel One Singularity Ranger",
        "sentinel_one": "Sentinel One",
        "servicenow_cmdb": "ServiceNow CMDB",
        "sevco_io": "Secvo Asset Management",
        "solarwinds_sd": "SolarWinds Service Desk",
        "tanium_em": "Tanium Endpoint Management",
        "tenable_io": "Tenable IO",
        "tm_vision_one": "Trend Micro Vision One",
        "ubiquity_unifi": "Ubiquity Unifi",
        "veeam": "Veeam",
        "veritas_alta_baas": "Veritas Alta BAAS",
        "vmware_aria": "VMware Aria",
        "zscaler": "Zscaler",
        "user_input": "(Internal) User Input",
        "demo_environment": "Demo Environment",
    }

    # Create Technology records for all known technologies
    for tech_id, tech_name in technology_names.items():
        Technology.objects.get_or_create(
            technology_id=tech_id,
            name=tech_name,
        )


class Migration(migrations.Migration):
    dependencies = [
        ("integrations", "0008_integration_endpoint_coverage_mode"),
    ]

    operations = [
        migrations.CreateModel(
            name="Technology",
            fields=[
                (
                    "technology_id",
                    models.CharField(
                        max_length=255, primary_key=True, serialize=False, unique=True
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Technology",
                "verbose_name_plural": "Technologies",
                "db_table": "integrations_technology",
            },
        ),

        migrations.RemoveField(
            model_name="integration",
            name="technology_id",
        ),

        migrations.AddField(
            model_name="integration",
            name="technology",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.RESTRICT,
                to="integrations.technology",
            ),
        ),

        # Populate Technology table and update Integration references
        migrations.RunPython(
            populate_technology_data,
        ),
    ]
