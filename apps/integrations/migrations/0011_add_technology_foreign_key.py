# Generated manually to add Technology foreign key to Integration

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0010_fix_integration_technology_relationship'),
    ]

    operations = [
        migrations.AddField(
            model_name='integration',
            name='technology',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.RESTRICT,
                to='integrations.technology',
            ),
        ),
    ]
